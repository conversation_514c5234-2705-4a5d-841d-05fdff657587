# 产品功能场景标准模板

> 本模板用于编写产品功能的具体使用场景，帮助产品、研发、测试达成对业务流程、用户行为与交互逻辑的一致理解，并可辅助自动化测试用例提取。

---

## 📌 功能名称  
（示例：更换绑定手机号）

## 📎 场景编号  
（示例：SC001）

## 🎯 场景名称  
（示例：用户原手机号仍可使用）

## 👤 用户与动机（Who + Why）  
- 用户角色（示例：老用户张伟）  
- 行为动机（示例：更换 SIM 卡后希望更新手机号以便接收通知）

## 🕒 情境与约束（When + Where + How）  
- 时间：如晚饭后  
- 地点：如家中  
- 网络：如 Wi-Fi 稳定  
- 心态与状态：如放松，有时间完成操作  
- 设备与平台：如 iPhone，使用 APP

## 🎯 功能目标（What）  
（示例：成功将原手机号 A 更换为新手机号 B，并获得系统确认）

---

## 🛣 主路径交互流程（Happy Path）

| 步骤编号 | 用户操作                             | 系统响应                                         |
|----------|--------------------------------------|--------------------------------------------------|
| 1        | 打开“账户与安全”                    | 显示当前绑定手机号                              |
| 2        | 点击“更换手机号”                    | 显示提示“我们将向原手机号 138****1234 发送验证码” |
| 3        | 点击“获取验证码”                    | 系统发送验证码至原手机号                        |
| 4        | 输入原手机号验证码                  | 验证通过，进入下一步                            |
| 5        | 输入新手机号并点击“获取验证码”     | 系统向新手机号发送验证码                        |
| 6        | 输入新手机号验证码并点击“完成”     | 提示“更换成功”，自动返回上级页面               |

---

## 🚧 异常路径及处理策略（Exception Flows）

| 异常编号 | 异常情况                         | 系统处理逻辑                                       | 用户提示                                   |
|----------|----------------------------------|----------------------------------------------------|--------------------------------------------|
| E1       | 60 秒内未收到验证码              | 按钮 60s 后变为可点击                              | “如未收到验证码请检查短信拦截或重新获取”  |
| E2       | 用户输入验证码错误               | 输入框清空，显示红色错误提示                      | “验证码错误，请重新输入”                   |
| E3       | 输入错误超过 3 次                 | 冻结验证码请求功能 2 分钟                          | “验证码错误次数过多，请稍后再试”           |

---

## ✅ 场景完成结果与价值

- 用户张伟成功完成了手机号更新操作；
- 系统响应及时、提示清晰、流程顺畅；
- 增强了用户对账户安全的信任感。

---

## 🧪 可提取测试用例列表

| 用例编号 | 用例名称                          | 前置条件                | 操作步骤                    | 预期结果                                |
|----------|-----------------------------------|-------------------------|-----------------------------|-----------------------------------------|
| TC001    | 成功更换手机号                    | 旧手机号可用             | 完成正常流程                | 手机号更换成功，提示“修改成功”         |
| TC002    | 输入错误验证码                    | 获取验证码成功           | 输入错误 1 次               | 提示“验证码错误，请重新输入”           |
| TC003    | 60 秒未收到验证码再次获取        | 第一次验证码未收到       | 60 秒后点击“重新获取”按钮   | 成功重新获取验证码                     |
| TC004    | 验证码输错超过 3 次后被限制       | 多次输错验证码           | 连续输错 3 次以上           | 按钮变灰，系统提示稍后再试             |

---

## 🏷 标签建议

- `#账号安全`
- `#手机验证码`
- `#用户体验`
- `#自动化可测`
- `#边界路径`

---

## 📝 设计备注（可选）

- 界面中应使用尾号遮掩手机号，如 138****1234；
- 验证码输入框应支持粘贴、自动跳转、粘贴识别；
- 验证码接口应有频控机制防止滥用；
- 可考虑支持「语音验证码」作为辅助方案。

